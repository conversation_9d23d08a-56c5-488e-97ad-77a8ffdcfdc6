#!/bin/bash

## 软件源列表
# 格式: "软件源名称@软件源地址"
# shellcheck disable=SC2034
mirror_list=(
    "自有源@mirrors.lilh.net"
    "中国科学技术大学@mirrors.ustc.edu.cn"
    "南京大学@mirrors.nju.edu.cn"
)

## DNS 服务器列表
dns_list=(
    "国内@119.29.29.29,223.5.5.5"
    "全球@1.1.1.1,8.8.8.8"
    "腾讯云@183.60.82.98,183.60.83.19"
)

## NTP 服务器列表
ntp_list=(
    "阿里云@ntp.aliyun.com,ntp1.aliyun.com,ntp2.aliyun.com,ntp3.aliyun.com,ntp4.aliyun.com,ntp5.aliyun.com,ntp6.aliyun.com,ntp7.aliyun.com"
    "阿里云内网@ntp.cloud.aliyuncs.com,ntp7.cloud.aliyuncs.com,ntp8.cloud.aliyuncs.com,ntp9.cloud.aliyuncs.com,ntp10.cloud.aliyuncs.com,ntp11.cloud.aliyuncs.com,ntp12.cloud.aliyuncs.com"
    "腾讯云@ntp.tencent.com,ntp1.tencent.com,ntp2.tencent.com,ntp3.tencent.com,ntp4.tencent.com,ntp5.tencent.com"
    "腾讯云内网@time1.tencentyun.com,time2.tencentyun.com,time3.tencentyun.com,time4.tencentyun.com,time5.tencentyun.com"
    "<EMAIL>"
    "<EMAIL>"
)
##############################################################################

## 定义系统判定变量
SYSTEM_DEBIAN="Debian"
SYSTEM_UBUNTU="Ubuntu"

## 定义系统版本文件
File_LinuxRelease=/etc/os-release
File_DebianVersion=/etc/debian_version

## 定义软件源相关文件或目录
File_DebianSourceList=/etc/apt/sources.list
File_DebianSources=/etc/apt/sources.list.d/debian.sources
File_UbuntuSources=/etc/apt/sources.list.d/ubuntu.sources
Dir_DebianExtendSource=/etc/apt/sources.list.d

## 定义颜色变量
RED='\033[31m'
GREEN='\033[32m'
YELLOW='\033[33m'
BLUE='\033[34m'
PURPLE='\033[35m'
AZURE='\033[36m'
PLAIN='\033[0m'
BOLD='\033[1m'
SUCCESS="[\033[1;32m成功${PLAIN}]"
COMPLETE="[\033[1;32m完成${PLAIN}]"
WARN="[\033[1;5;33m注意${PLAIN}]"
ERROR="[\033[1;31m错误${PLAIN}]"
FAIL="[\033[1;31m失败${PLAIN}]"
TIP="[\033[1;34m提示${PLAIN}]"
WORKING="[\033[1;36m >_ ${PLAIN}]"

##############################################################################

## 计算字符串长度的函数
function StringLength() {
    local text=$1
    echo "${#text}"
}

##############################################################################

function main() {
    permission_judgment
    collect_system_info
    run_start
    change_root_password
    choose_mirrors
    close_firewall_service
    remove_original_mirrors
    change_mirrors_main
    upgrade_software
    check_system_users
    configure_ssh
    set_up_dns
    set_up_ntp
    manage_disk
    modify_hostname
    modify_files
    enable_bbr
    enable_zram
    install_tools
    install_edgezero
    run_end
    ask_reboot
}

##############################################################################

## 权限判定
function permission_judgment() {
    if [ $UID -ne 0 ]; then
        output_error "请使用 root 用户运行"
    fi
}

## 收集系统信息
function collect_system_info() {
    ## 定义系统名称
    SYSTEM_NAME="$(cat $File_LinuxRelease | grep -E "^NAME=" | awk -F '=' '{print$2}' | sed "s/[\'\"]//g")"
    grep -q "PRETTY_NAME=" $File_LinuxRelease && SYSTEM_PRETTY_NAME="$(cat $File_LinuxRelease | grep -E "^PRETTY_NAME=" | awk -F '=' '{print$2}' | sed "s/[\'\"]//g")"
    ## 定义系统版本号
    SYSTEM_VERSION_NUMBER="$(cat $File_LinuxRelease | grep -E "^VERSION_ID=" | awk -F '=' '{print$2}' | sed "s/[\'\"]//g")"
    ## 定义系统 ID
    SYSTEM_ID="$(cat $File_LinuxRelease | grep -E "^ID=" | awk -F '=' '{print$2}' | sed "s/[\'\"]//g")"
    ## 判断当前系统发行版本
    if [ -s $File_DebianVersion ]; then
        SYSTEM_FACTIONS="${SYSTEM_DEBIAN}"
    else
        output_error "无法判断当前运行环境"
    fi
    ## 判定系统类型、版本、版本号
    case "${SYSTEM_FACTIONS}" in
    "${SYSTEM_DEBIAN}")
        if [ ! -x /usr/bin/lsb_release ]; then
            apt-get install -y lsb-release
            # shellcheck disable=SC2181
            if [ $? -ne 0 ]; then
                output_error "lsb-release 软件包安装失败\n\n本脚本依赖 lsb_release 指令判断系统具体类型和版本, 当前系统可能为精简安装, 请自行安装后重新执行脚本."
            fi
        fi
        SYSTEM_JUDGMENT="$(lsb_release -is)"
        SYSTEM_VERSION_CODENAME="${DEBIAN_CODENAME:-"$(lsb_release -cs)"}"
        ;;
    *)
        SYSTEM_JUDGMENT="${SYSTEM_FACTIONS}"
        ;;
    esac
    ## 判断系统及版本是否受适配
    local is_supported="true"
    case "${SYSTEM_JUDGMENT}" in
    "${SYSTEM_DEBIAN}")
        if [[ "${SYSTEM_VERSION_NUMBER:0:1}" != [8-9] && "${SYSTEM_VERSION_NUMBER:0:2}" != 1[0-3] ]]; then
            is_supported="false"
        fi
        ;;
    "${SYSTEM_UBUNTU}")
        if [[ "${SYSTEM_VERSION_NUMBER:0:2}" != 1[4-9] && "${SYSTEM_VERSION_NUMBER:0:2}" != 2[0-4] ]]; then
            is_supported="false"
        fi
        ;;
    *)
        is_supported="false"
        ;;
    esac
    if [[ "${is_supported}" == "false" ]]; then
        output_error "当前系统版本不在本脚本的支持范围内"
    fi
    ## 判定系统处理器架构
    case "$(uname -m)" in
    x86_64)
        DEVICE_ARCH="x86_64"
        ;;
    aarch64)
        DEVICE_ARCH="ARM64"
        ;;
    armv7l)
        DEVICE_ARCH="ARMv7"
        ;;
    armv6l)
        DEVICE_ARCH="ARMv6"
        ;;
    i686)
        DEVICE_ARCH="x86_32"
        ;;
    *)
        DEVICE_ARCH="$(uname -m)"
        ;;
    esac
    ## 定义软件源分支名称
    if [[ -z "${SOURCE_BRANCH}" ]]; then
        ## 默认为系统名称小写, 替换空格
        SOURCE_BRANCH="${SYSTEM_JUDGMENT,,}"
        SOURCE_BRANCH="${SOURCE_BRANCH// /-}"
        ## 处理特殊的分支名称
        case "${SYSTEM_JUDGMENT}" in
        "${SYSTEM_DEBIAN}")
            case ${SYSTEM_VERSION_NUMBER:0:1} in
            8 | 9 | 10)
                SOURCE_BRANCH="debian-archive" # EOF
                ;;
            *)
                SOURCE_BRANCH="debian"
                ;;
            esac
            ;;
        "${SYSTEM_UBUNTU}")
            if [[ "${DEVICE_ARCH}" == "x86_64" ]] || [[ "${DEVICE_ARCH}" == *i?86* ]]; then
                SOURCE_BRANCH="ubuntu"
            else
                SOURCE_BRANCH="ubuntu-ports"
            fi
            ;;
        esac
    fi
    ## 定义软件源更新文字
    case "${SYSTEM_FACTIONS}" in
    "${SYSTEM_DEBIAN}")
        SYNC_MIRROR_TEXT="更新软件源"
        ;;
    esac
}

function run_start() {
    [ -z "${SOURCE}" ] && clear
    echo -e ' +-----------------------------------+'
    echo -e " | \033[0;1;35;95m⡇\033[0m  \033[0;1;33;93m⠄\033[0m \033[0;1;32;92m⣀⡀\033[0m \033[0;1;36;96m⡀\033[0;1;34;94m⢀\033[0m \033[0;1;35;95m⡀⢀\033[0m \033[0;1;31;91m⡷\033[0;1;33;93m⢾\033[0m \033[0;1;32;92m⠄\033[0m \033[0;1;36;96m⡀⣀\033[0m \033[0;1;34;94m⡀\033[0;1;35;95m⣀\033[0m \033[0;1;31;91m⢀⡀\033[0m \033[0;1;33;93m⡀\033[0;1;32;92m⣀\033[0m \033[0;1;36;96m⢀⣀\033[0m |"
    echo -e " | \033[0;1;31;91m⠧\033[0;1;33;93m⠤\033[0m \033[0;1;32;92m⠇\033[0m \033[0;1;36;96m⠇⠸\033[0m \033[0;1;34;94m⠣\033[0;1;35;95m⠼\033[0m \033[0;1;31;91m⠜⠣\033[0m \033[0;1;33;93m⠇\033[0;1;32;92m⠸\033[0m \033[0;1;36;96m⠇\033[0m \033[0;1;34;94m⠏\033[0m  \033[0;1;35;95m⠏\033[0m  \033[0;1;33;93m⠣⠜\033[0m \033[0;1;32;92m⠏\033[0m  \033[0;1;34;94m⠭⠕\033[0m |"
    echo -e ' +-----------------------------------+'
    echo -e ' 开始进行 GNU/Linux 环境配置执行检查'
    function print_title() {
        local system_name="${SYSTEM_PRETTY_NAME:-"${SYSTEM_NAME} ${SYSTEM_VERSION_NUMBER}"}"
        local arch="${DEVICE_ARCH}"
        local date_time time_zone
        date_time="$(date "+%Y-%m-%d %H:%M:%S")"
        time_zone="$(timedatectl status 2>/dev/null | grep "Time zone" | awk -F ':' '{print$2}' | awk -F ' ' '{print$1}')"

        echo -e ''
        echo -e " 操作系统 ${BLUE}${system_name} ${arch}${PLAIN}"
        echo -e " 系统时间 ${BLUE}${date_time} ${time_zone}${PLAIN}"
    }
    print_title
}

## 修改 root 用户密码
function change_root_password() {
    local CHOICE
    CHOICE=$(echo -e "\n${BOLD}└─ 是否修改当前 root 用户密码? [Y/n] ${PLAIN}")
    read -rp "${CHOICE}" INPUT
    [[ -z "${INPUT}" ]] && INPUT=Y
    case "${INPUT}" in
    [Yy] | [Yy][Ee][Ss])
        while true; do
            echo -e "\n$WORKING 开始修改 root 密码...\n"
            passwd root
            # shellcheck disable=SC2181
            if [ $? -eq 0 ]; then
                echo -e "\n$SUCCESS root 密码修改成功"
                break
            else
                echo -e "\n$FAIL root 密码修改失败"
                CHOICE=$(echo -e "\n${BOLD}└─ 是否重新尝试修改密码? [Y/n] ${PLAIN}")
                read -rp "${CHOICE}" RETRY
                [[ -z "${RETRY}" ]] && RETRY=Y
                case "${RETRY}" in
                [Yy] | [Yy][Ee][Ss])
                    continue
                    ;;
                *)
                    echo -e "\n$TIP 取消修改 root 密码"
                    break
                    ;;
                esac
            fi
        done
        ;;
    [Nn] | [Nn][Oo])
        echo -e "\n$TIP 跳过 root 密码修改"
        ;;
    *)
        echo -e "\n$WARN 输入错误, 默认不修改 root 密码."
        ;;
    esac
}

## 选择软件源
function choose_mirrors() {
    ## 打印软件源列表
    function print_mirrors_list() {
        local tmp_mirror_name tmp_mirror_url arr_num default_mirror_name_length tmp_mirror_name_length tmp_spaces_nums a i j
        ## 计算字符串长度
        function StringLength() {
            local text=$1
            echo "${#text}"
        }
        echo -e ''

        local list_arr=()
        local list_arr_sum
        # shellcheck disable=SC1083
        list_arr_sum="$(eval echo \${#"$1"[@]})"
        # shellcheck disable=SC2004
        for ((a = 0; a < $list_arr_sum; a++)); do
            # shellcheck disable=SC1083
            list_arr[$a]="$(eval echo \${"$1"[a]})"
        done
        if [ -x /usr/bin/printf ]; then
            for ((i = 0; i < ${#list_arr[@]}; i++)); do
                tmp_mirror_name=$(echo "${list_arr[i]}" | awk -F '@' '{print$1}') # 软件源名称
                tmp_mirror_url=$(echo "${list_arr[i]}" | awk -F '@' '{print$2}') # 软件源地址
                arr_num=$((i + 1))
                default_mirror_name_length=${2:-"30"} # 默认软件源名称打印长度
                ## 补齐长度差异 (中文的引号在等宽字体中占 1 格而非 2 格)
                # shellcheck disable=SC1111
                # shellcheck disable=SC2219
                [[ $(echo "${tmp_mirror_name}" | grep -c "“") -gt 0 ]] && let default_mirror_name_length+=$(echo "${tmp_mirror_name}" | grep -c "“")
                # shellcheck disable=SC1111
                # shellcheck disable=SC2219
                [[ $(echo "${tmp_mirror_name}" | grep -c "”") -gt 0 ]] && let default_mirror_name_length+=$(echo "${tmp_mirror_name}" | grep -c "”")
                # shellcheck disable=SC2219
                [[ $(echo "${tmp_mirror_name}" | grep -c "‘") -gt 0 ]] && let default_mirror_name_length+=$(echo "${tmp_mirror_name}" | grep -c "‘")
                # shellcheck disable=SC2219
                [[ $(echo "${tmp_mirror_name}" | grep -c "’") -gt 0 ]] && let default_mirror_name_length+=$(echo "${tmp_mirror_name}" | grep -c "’")
                # 非一般字符长度
                # shellcheck disable=SC2001
                tmp_mirror_name_length=$(StringLength "$(echo "${tmp_mirror_name// /}" | sed "s|[0-9a-zA-Z\.\=\:\_$$\'\"-\/\!·]||g;")")
                ## 填充空格
                tmp_spaces_nums=$(($((default_mirror_name_length - tmp_mirror_name_length - $(StringLength "${tmp_mirror_name}"))) / 2))
                # shellcheck disable=SC2004
                for ((j = 1; j <= ${tmp_spaces_nums}; j++)); do
                    tmp_mirror_name="${tmp_mirror_name} "
                done
                # 打印软件源名称、地址和序号在同一行
                printf " ❖  %-$((default_mirror_name_length + tmp_mirror_name_length))s %-25s %2s\n" "${tmp_mirror_name}" "${tmp_mirror_url}" "$arr_num)"
            done
        else
            for ((i = 0; i < ${#list_arr[@]}; i++)); do
                tmp_mirror_name=$(echo "${list_arr[i]}" | awk -F '@' '{print$1}') # 软件源名称
                tmp_mirror_url=$(echo "${list_arr[i]}" | awk -F '@' '{print$2}')  # 软件源地址
                arr_num=$((i + 1))
                echo -e " ❖  $arr_num. ${tmp_mirror_name} | ${tmp_mirror_url}"
            done
        fi
    }

    if [[ -z "${SOURCE}" ]]; then
        local mirror_list_name="mirror_list"
        print_mirrors_list "${mirror_list_name}" 31

        local CHOICE
        # shellcheck disable=SC1087
        # shellcheck disable=SC1083
        CHOICE=$(echo -e "\n${BOLD}└─ 请选择并输入你想使用的软件源 [ 1-$(eval echo \${#$mirror_list_name[@]}) ]: ${PLAIN}")
        while true; do
            read -rp "${CHOICE}" INPUT
            case "${INPUT}" in
            [1-9] | [1-9][0-9] | [1-9][0-9][0-9])
                local tmp_source
                # shellcheck disable=SC1083
                tmp_source="$(eval echo \${${mirror_list_name}[$((INPUT - 1))]})"
                if [[ -z "${tmp_source}" ]]; then
                    echo -e "\n$WARN 请输入有效的数字序号"
                else
                    # shellcheck disable=SC1083
                    SOURCE="$(eval echo \${${mirror_list_name}[$((INPUT - 1))]} | awk -F '@' '{print$2}')"
                    break
                fi
                ;;
            *)
                echo -e "\n$WARN 请输入数字序号以选择你想使用的软件源"
                ;;
            esac
        done
    fi
}

## 关闭防火墙和 SELinux
function close_firewall_service() {
    if [ ! -x /usr/bin/systemctl ]; then
        return
    fi
    if [[ "$(systemctl is-active firewalld)" == "active" ]]; then
        if [[ -z "${CLOSE_FIREWALL}" ]]; then
            local CHOICE
            CHOICE=$(echo -e "\n${BOLD}└─ 是否关闭防火墙和 SELinux ? [Y/n] ${PLAIN}")
            read -rp "${CHOICE}" INPUT
            [[ -z "${INPUT}" ]] && INPUT=Y
            case "${INPUT}" in
            [Yy] | [Yy][Ee][Ss])
                CLOSE_FIREWALL="true"
                ;;
            [Nn] | [Nn][Oo]) ;;
            *)
                echo -e "\n$WARN 输入错误, 默认不关闭."
                ;;
            esac
        fi
        if [[ "${CLOSE_FIREWALL}" == "true" ]]; then
            local SelinuxConfig=/etc/selinux/config
            systemctl disable --now firewalld >/dev/null 2>&1
            [ -s $SelinuxConfig ] && sed -i "s/SELINUX=enforcing/SELINUX=disabled/g" $SelinuxConfig && setenforce 0 >/dev/null 2>&1
        fi
    fi
}

## 移除原有软件源
function remove_original_mirrors() {
    case "${SYSTEM_FACTIONS}" in
    "${SYSTEM_DEBIAN}")
        [ -f $File_DebianSourceList ] && sed -i '1,$d' $File_DebianSourceList
        [ -d $Dir_DebianExtendSource ] || mkdir -p $Dir_DebianExtendSource
        ## 自新版本的 Debian 与 Ubuntu 起, 软件源文件格式统一为 DEB822 格式, 涉及 Debian 12 的容器镜像、Ubuntu 24.04 和未来尚未发布的版本
        # Debian DEB822 格式源文件
        if [[ "${SYSTEM_JUDGMENT}" == "${SYSTEM_DEBIAN}" ]]; then
            [ -f $File_DebianSources ] && rm -rf $File_DebianSources
        fi
        # Ubuntu DEB822 格式源文件
        if [[ "${SYSTEM_JUDGMENT}" == "${SYSTEM_UBUNTU}" ]]; then
            [ -f $File_UbuntuSources ] && rm -rf $File_UbuntuSources
        fi
        ;;
    esac
}

## 换源
function change_mirrors_main() {
    ## 调用换源函数
    case "${SYSTEM_FACTIONS}" in
    "${SYSTEM_DEBIAN}")
        change_mirrors_Debian
        ;;
    esac
    ## 更新软件源
    echo -e "\n$WORKING 开始${SYNC_MIRROR_TEXT}...\n"
    case "${SYSTEM_FACTIONS}" in
    "${SYSTEM_DEBIAN}")
        apt-get update
        ;;
    esac
    # shellcheck disable=SC2181
    if [ $? -eq 0 ]; then
        echo -e "\n$SUCCESS 软件源更换完毕"
    else
        echo -e "\n$FAIL 软件源更换完毕, 但${SYNC_MIRROR_TEXT}失败.\n"
        exit 1
    fi
}

## 升级软件包
function upgrade_software() {
    function clean_cache() {
        ## 交互确认
        if [[ -z "${CLEAN_CACHE}" ]]; then
            CLEAN_CACHE="false"
            local CHOICE
            CHOICE=$(echo -e "\n${BOLD}└─ 是否清理已下载的软件包缓存? [Y/n] ${PLAIN}")
            read -rp "${CHOICE}" INPUT
            [[ -z "${INPUT}" ]] && INPUT=Y
            case "${INPUT}" in
            [Yy] | [Yy][Ee][Ss])
                CLEAN_CACHE="true"
                ;;
            [Nn] | [Nn][Oo]) ;;
            *)
                echo -e "\n$WARN 输入错误, 默认不清理."
                ;;
            esac
        fi
        if [[ "${CLEAN_CACHE}" == "false" ]]; then
            return
        fi
        ## 调用系统命令
        case "${SYSTEM_FACTIONS}" in
        "${SYSTEM_DEBIAN}")
            apt-get autoremove -y >/dev/null 2>&1
            apt-get clean >/dev/null 2>&1
            ;;
        esac
        echo -e "\n$COMPLETE 清理完毕"
    }

    ## 交互确认
    if [[ -z "${UPGRADE_SOFTWARE}" ]]; then
        UPGRADE_SOFTWARE="true"
        local CHOICE
        CHOICE=$(echo -e "\n${BOLD}└─ 是否执行更新软件包? [Y/n] ${PLAIN}")
        read -rp "${CHOICE}" INPUT
        [[ -z "${INPUT}" ]] && INPUT=Y
        case "${INPUT}" in
        [Yy] | [Yy][Ee][Ss]) ;;
        [Nn] | [Nn][Oo])
            UPGRADE_SOFTWARE="false"
            ;;
        *)
            echo -e "\n$WARN 输入错误, 默认不更新."
            ;;
        esac
    fi
    if [[ "${UPGRADE_SOFTWARE}" == "false" ]]; then
        return
    fi
    echo -e ''
    ## 调用系统命令
    case "${SYSTEM_FACTIONS}" in
    "${SYSTEM_DEBIAN}")
        apt-get upgrade -y
        ;;
    esac
    ## 清理缓存
    clean_cache
}

## 检查系统账户
function check_system_users() {
    echo -e "\n$WORKING 正在检查系统用户..."

    echo -e "\n当前系统用户列表:\n"
    printf " %-20s │ %-5s │ %-5s │ %-20s │ %-10s\n" "User" "UID" "GID" "Shell" "Permissions"
    printf " %-20s │ %-5s │ %-5s │ %-20s │ %-10s\n" "--------------------" "-----" "-----" "--------------------" "----------"

    while IFS=: read -r username password uid gid info home shell; do
        # shellcheck disable=SC2155
        # shellcheck disable=SC2012
        local permissions=$(ls -ld "$home" 2>/dev/null | awk '{print $1}')
        [ -z "$permissions" ] && permissions="N/A"
        printf " %-20s │ %-5s │ %-5s │ %-20s │ %-10s\n" "$username" "$uid" "$gid" "$shell" "$permissions"
    done < /etc/passwd

    local CHOICE
    CHOICE=$(echo -e "\n${BOLD}└─ 是否需要删除用户? [Y/n] ${PLAIN}")
    read -rp "${CHOICE}" INPUT
    [[ -z "${INPUT}" ]] && INPUT=Y
    case "${INPUT}" in
    [Yy] | [Yy][Ee][Ss])
        local USERNAMES
        while true; do
            CHOICE=$(echo -e "\n${BOLD}└─ 请输入要删除的用户名 (多个用户用逗号分隔): ${PLAIN}")
            read -rp "${CHOICE}" USERNAMES
            if [[ -z "${USERNAMES}" ]]; then
                echo -e "\n$WARN 输入不能为空, 请重新输入."
            else
                break
            fi
        done

        IFS=',' read -ra USERS <<< "$USERNAMES"
        for USERNAME in "${USERS[@]}"; do
            USERNAME=$(echo "$USERNAME" | xargs)  # 去除前后空格
            if grep -q "^$USERNAME:" /etc/passwd; then
                CHOICE=$(echo -e "\n${BOLD}└─ 您确定要删除用户 $USERNAME 吗? 这可能是系统用户! [Y/n] ${PLAIN}")
                read -rp "${CHOICE}" CONFIRM
                [[ -z "${CONFIRM}" ]] && CONFIRM=N
                case "${CONFIRM}" in
                [Yy] | [Yy][Ee][Ss])
                    echo -e "\n$WORKING 正在删除用户 $USERNAME ...\n"
                    userdel -r "$USERNAME"
                    # shellcheck disable=SC2181
                    if [ $? -eq 0 ]; then
                        echo -e "\n$SUCCESS 用户 $USERNAME 已成功删除"
                    else
                        echo -e "\n$FAIL 删除用户 $USERNAME 失败"
                    fi
                    ;;
                *)
                    echo -e "\n$TIP 已取消删除用户 $USERNAME"
                    ;;
                esac
            else
                echo -e "\n$WARN 用户 $USERNAME 不存在"
            fi
        done
        ;;
    [Nn] | [Nn][Oo])
        echo -e "\n$TIP 跳过用户删除操作"
        ;;
    *)
        echo -e "\n$WARN 输入错误, 默认不删除用户."
        ;;
    esac
}

## 检查并修改 ssh 配置
function configure_ssh() {
    echo -e "\n$WORKING 正在检查 SSH 配置..."

    local SSH_CONFIG="/etc/ssh/sshd_config"
    local AUTH_KEYS_FILE="$HOME/.ssh/authorized_keys"
    local CURRENT_SSH_PORT
    local SSH_PORT
    local CURRENT_AUTH_METHOD
    local CONFIG_CHANGED=false

    # 更新 ssh 配置项
    function update_ssh_config() {
        local key="$1"
        local value="$2"
        local config_file="$3"

        # shellcheck disable=SC2155
        local existing_line=$(grep -n "^#*${key}\s" "$config_file" | head -n1)

        if [ -n "$existing_line" ]; then
            # shellcheck disable=SC2155
            local line_number=$(echo "$existing_line" | cut -d: -f1)
            # shellcheck disable=SC2155
            local current_line=$(echo "$existing_line" | cut -d: -f2-)

            if echo "$current_line" | grep -q "${value}$"; then
                if echo "$current_line" | grep -q "^#"; then
                    echo -e "\n$TIP 保留注释的默认配置: $current_line"
                else
                    echo -e "\n$TIP 配置 $key 已经是 $value, 无需修改."
                fi
            elif echo "$current_line" | grep -q "^#"; then
                sed -i "${line_number}s/^#*${key}.*/${key} ${value}/" "$config_file"
                echo -e "\n$SUCCESS 已取消注释并修改配置: ${key} ${value}"
            else
                sed -i "${line_number}s/^${key}.*/${key} ${value}/" "$config_file"
                echo -e "\n$SUCCESS 已修改配置: ${key} ${value}"
            fi
        else
            echo "${key} ${value}" >> "$config_file"
            echo -e "\n$SUCCESS 已添加新配置: ${key} ${value}"
        fi

        sed -i "${line_number}!{/^#*${key}\s/d}" "$config_file"
    }

    # 检查当前认证方式
    function check_auth_method() {
        # shellcheck disable=SC2155
        local password_auth=$(grep -E "^PasswordAuthentication\s+(yes|no)" "$SSH_CONFIG" | awk '{print $2}')
        # shellcheck disable=SC2155
        local pubkey_auth=$(grep -E "^PubkeyAuthentication\s+(yes|no)" "$SSH_CONFIG" | awk '{print $2}')
        # shellcheck disable=SC2155
        local root_login=$(grep -E "^PermitRootLogin\s+" "$SSH_CONFIG" | awk '{print $2}')

        [ -z "$password_auth" ] && password_auth=$(grep -E "^#PasswordAuthentication\s+(yes|no)" "$SSH_CONFIG" | awk '{print $2}')
        [ -z "$pubkey_auth" ] && pubkey_auth=$(grep -E "^#PubkeyAuthentication\s+(yes|no)" "$SSH_CONFIG" | awk '{print $2}')
        [ -z "$root_login" ] && root_login=$(grep -E "^#PermitRootLogin\s+" "$SSH_CONFIG" | awk '{print $2}')

        local key_file_exists=false
        local key_file_not_empty=false
        [ -f "$AUTH_KEYS_FILE" ] && key_file_exists=true
        [ -s "$AUTH_KEYS_FILE" ] && key_file_not_empty=true

        if [ "$password_auth" = "no" ] && [ "$pubkey_auth" = "yes" ] && $key_file_exists && $key_file_not_empty; then
            echo "key"
        elif [ "$password_auth" = "yes" ] && [ "$pubkey_auth" = "yes" ] && $key_file_exists && $key_file_not_empty; then
            echo "password_and_key"
        elif [ "$password_auth" = "yes" ] && ([ "$pubkey_auth" != "yes" ] || ! $key_file_exists || ! $key_file_not_empty); then
            echo "password"
        elif [ "$root_login" = "prohibit-password" ] && $key_file_exists && $key_file_not_empty; then
            echo "key"
        else
            echo "unknown"
        fi
    }

    CURRENT_AUTH_METHOD=$(check_auth_method)

    # 如果无法确定认证方式, 询问用户
    if [ "$CURRENT_AUTH_METHOD" = "unknown" ]; then
        echo -e "\n$WARN 无法自动确定当前 SSH 认证方式"
        CHOICE=$(echo -e "\n${BOLD}└─ 请选择当前的认证方式: [1]密码 [2]密钥 [3]密码和密钥 ${PLAIN}")
        read -rp "${CHOICE}" AUTH_CHOICE
        case "$AUTH_CHOICE" in
            1) CURRENT_AUTH_METHOD="password" ;;
            2) CURRENT_AUTH_METHOD="key" ;;
            3) CURRENT_AUTH_METHOD="password_and_key" ;;
            *) echo -e "\n$ERROR 无效的选择, 请手动检查 SSH 配置."; return 1 ;;
        esac
    fi

    # 获取当前 ssh 端口
    CURRENT_SSH_PORT=$(grep "^Port " $SSH_CONFIG | awk '{print $2}')
    [ -z "$CURRENT_SSH_PORT" ] && CURRENT_SSH_PORT=22

    # 修改 ssh 端口
    CHOICE=$(echo -e "\n${BOLD}└─ 是否需要修改 SSH 端口? [ $CURRENT_SSH_PORT ] [Y/n] ${PLAIN}")
    read -rp "${CHOICE}" INPUT
    case "${INPUT}" in
    [Yy] | [Yy][Ee][Ss] | "")
        CHOICE=$(echo -e "\n${BOLD}└─ 请输入新的 SSH 端口 [1-65535]: ${PLAIN}")
        read -rp "${CHOICE}" SSH_PORT
        if [[ "$SSH_PORT" =~ ^[0-9]+$ ]] && [ "$SSH_PORT" -ge 1 ] && [ "$SSH_PORT" -le 65535 ]; then
            if [ "$SSH_PORT" != "$CURRENT_SSH_PORT" ]; then
                update_ssh_config "Port" "$SSH_PORT" "$SSH_CONFIG"
                echo -e "\n$SUCCESS SSH 端口已修改为 $SSH_PORT"
                CONFIG_CHANGED=true
            else
                # shellcheck disable=SC2153
                echo -e "\n$INFO 新端口与当前端口相同, 无需修改."
            fi
        else
            echo -e "\n$WARN 无效的端口号, 保持原有设置."
        fi
        ;;
    *)
        echo -e "\n$TIP 保持原有 SSH 端口设置"
        ;;
    esac

    # 配置认证方式
    function configure_auth_method() {
        local target_method="$1"
        update_ssh_config "PermitRootLogin" "prohibit-password" "$SSH_CONFIG"
        update_ssh_config "PubkeyAuthentication" "yes" "$SSH_CONFIG"
        update_ssh_config "PasswordAuthentication" "no" "$SSH_CONFIG"

        echo -e "\n$SUCCESS SSH 已配置为仅允许密钥登录"
        CONFIG_CHANGED=true

        if [ ! -f "$AUTH_KEYS_FILE" ] || [ ! -s "$AUTH_KEYS_FILE" ]; then
            echo -e "\n$WARN authorized_keys 文件不存在或为空, 请确保已添加公钥."
            CHOICE=$(echo -e "\n${BOLD}└─ 是否需要添加公钥? [Y/n] ${PLAIN}")
            read -rp "${CHOICE}" ADD_KEY
            case "${ADD_KEY}" in
            [Yy] | [Yy][Ee][Ss] | "")
                CHOICE=$(echo -e "\n${BOLD}└─ 请粘贴您的公钥: ${PLAIN}")
                read -rp "${CHOICE}" PUBLIC_KEY
                if [ -n "$PUBLIC_KEY" ]; then
                    mkdir -p ~/.ssh
                    echo "$PUBLIC_KEY" >> "$AUTH_KEYS_FILE"
                    chmod 700 ~/.ssh
                    chmod 600 "$AUTH_KEYS_FILE"
                    if [ -s "$AUTH_KEYS_FILE" ]; then
                        echo -e "\n$SUCCESS 公钥已成功添加"
                    else
                        echo -e "\n$FAIL 公钥添加失败, authorized_keys 文件仍为空."
                    fi
                else
                    echo -e "\n$WARN 未输入公钥, 请确保后续能够登录."
                fi
                ;;
            *)
                echo -e "\n$WARN 未添加公钥, 请确保后续能够登录."
                ;;
            esac
        fi
    }

    case "$CURRENT_AUTH_METHOD" in
    "key")
        echo -e "\n$TIP 当前已默认使用密钥登录, 跳过认证方式配置."
        ;;
    "password_and_key")
        CHOICE=$(echo -e "\n${BOLD}└─ 当前允许使用密码和密钥同时登录, 是否仅允许密钥登录? [Y/n] ${PLAIN}")
        read -rp "${CHOICE}" KEY_ONLY
        case "${KEY_ONLY}" in
        [Yy] | [Yy][Ee][Ss] | "")
            configure_auth_method "key"
            ;;
        *)
            echo -e "\n$TIP 保持当前登录方式"
            ;;
        esac
        ;;
    "password")
        CHOICE=$(echo -e "\n${BOLD}└─ 当前仅允许使用密码登录, 是否仅允许密钥登录? [Y/n] ${PLAIN}")
        read -rp "${CHOICE}" KEY_ONLY
        case "${KEY_ONLY}" in
        [Yy] | [Yy][Ee][Ss] | "")
            configure_auth_method "key"
            ;;
        *)
            echo -e "\n$TIP 保持当前登录方式"
            ;;
        esac
        ;;
    *)
        echo -e "\n$WARN 无法确定当前 SSH 认证方式, 请手动检查配置."
        ;;
    esac

    if [ "$CONFIG_CHANGED" = true ]; then
        echo -e "\n$WORKING 正在重启 SSH 服务..."
        if systemctl restart sshd; then
            echo -e "\n$SUCCESS SSH 服务已重启"
        else
            echo -e "\n$FAIL SSH 服务重启失败, 请手动检查."
        fi
    else
        echo -e "\n$TIP SSH 配置未发生变更, 无需重启服务."
    fi
}

## 打印 DNS 服务器列表
function print_dns_list() {
    local tmp_dns_name tmp_dns_servers arr_num default_dns_name_length tmp_dns_name_length tmp_spaces_nums i j

    echo -e ''

    if [ -x /usr/bin/printf ]; then
        for ((i = 0; i < ${#dns_list[@]}; i++)); do
            tmp_dns_name=$(echo "${dns_list[i]}" | awk -F '@' '{print$1}')
            tmp_dns_servers=$(echo "${dns_list[i]}" | awk -F '@' '{print$2}')
            arr_num=$((i + 1))
            default_dns_name_length=15  # 默认 DNS 名称打印长度

            # 补齐长度差异 (中文的引号在等宽字体中占 1 格而非 2 格)
            # shellcheck disable=SC1111
            [[ $(echo "${tmp_dns_name}" | grep -c "“") -gt 0 ]] && ((default_dns_name_length+=$(echo "${tmp_dns_name}" | grep -c "“")))
            # shellcheck disable=SC1111
            [[ $(echo "${tmp_dns_name}" | grep -c "”") -gt 0 ]] && ((default_dns_name_length+=$(echo "${tmp_dns_name}" | grep -c "”")))
            [[ $(echo "${tmp_dns_name}" | grep -c "‘") -gt 0 ]] && ((default_dns_name_length+=$(echo "${tmp_dns_name}" | grep -c "‘")))
            [[ $(echo "${tmp_dns_name}" | grep -c "’") -gt 0 ]] && ((default_dns_name_length+=$(echo "${tmp_dns_name}" | grep -c "’")))

            # 非一般字符长度
            # shellcheck disable=SC2001
            tmp_dns_name_length=$(StringLength "$(echo "${tmp_dns_name// /}" | sed "s|[0-9a-zA-Z\.\=\:\_\$\'\"-\/\!·]||g;")")

            # 填充空格
            tmp_spaces_nums=$(( (default_dns_name_length - tmp_dns_name_length - $(StringLength "${tmp_dns_name}")) / 2 ))
            for ((j = 1; j <= tmp_spaces_nums; j++)); do
                tmp_dns_name="${tmp_dns_name} "
            done

            # 打印 DNS 名称、服务器和序号在同一行
            printf " ❖  %-$((default_dns_name_length + tmp_dns_name_length))s %-30s %2s\n" "${tmp_dns_name}" "${tmp_dns_servers}" "$arr_num)"
        done
    else
        for ((i = 0; i < ${#dns_list[@]}; i++)); do
            tmp_dns_name=$(echo "${dns_list[i]}" | awk -F '@' '{print$1}')
            tmp_dns_servers=$(echo "${dns_list[i]}" | awk -F '@' '{print$2}')
            arr_num=$((i + 1))
            echo -e " ❖  $arr_num. ${tmp_dns_name} | ${tmp_dns_servers}"
        done
    fi
}

## 配置 DNS
function set_up_dns() {
    local dns_configure_file="/etc/dnsmasq.conf"
    local dns_servers
    local dns_servers_text

    echo -e "\n$WORKING 正在配置 DNS 服务...\n"

    # 停用 systemd-resolved 服务
    if systemctl is-active --quiet systemd-resolved; then
        systemctl disable systemd-resolved
        systemctl stop systemd-resolved
    fi

    # 安装 dnsmasq
    apt-get install -y dnsmasq

    # 检查 /etc/resolv.conf 中的 DNS 服务器配置
    dns_servers=$(grep '^nameserver' /etc/resolv.conf | awk '{print $2}')
    if [[ -n "$dns_servers" ]]; then
        echo -e "\n$TIP 当前的 DNS 服务器配置为:\n"
        echo "$dns_servers"
        CHOICE=$(echo -e "\n${BOLD}└─ 是否需要修改 DNS 服务器配置? [Y/n] ${PLAIN}")
        read -rp "${CHOICE}" INPUT
        [[ -z "${INPUT}" ]] && INPUT=Y
        case "${INPUT}" in
        [Yy] | [Yy][Ee][Ss])
            ;;
        [Nn] | [Nn][Oo])
            echo -e "\n$TIP 保持当前 DNS 服务器配置"
            return
            ;;
        *)
            echo -e "\n$WARN 输入错误, 默认不修改 DNS 服务器配置."
            return
            ;;
        esac
    fi

    # 打印 DNS 服务器列表
    print_dns_list

    # 用户选择 DNS 服务器
    CHOICE=$(echo -e "\n${BOLD}└─ 请选择并输入你想使用的 DNS 服务器 [ 1-${#dns_list[@]} ] 或输入 'c' 自定义: ${PLAIN}")
    while true; do
        read -rp "${CHOICE}" INPUT
        case "${INPUT}" in
        [1-9] | [1-9][0-9])
            if [[ $INPUT -le ${#dns_list[@]} ]]; then
                dns_servers=$(echo "${dns_list[$((INPUT - 1))]}" | awk -F '@' '{print$2}')
                break
            else
                echo -e "\n$WARN 请输入有效的数字序号"
            fi
            ;;
        [Cc])
            CHOICE=$(echo -e "\n${BOLD}└─ 请输入自定义 DNS 服务器 (多个服务器用逗号分隔): ${PLAIN}")
            read -rp "${CHOICE}" dns_servers
            break
            ;;
        *)
            echo -e "\n$WARN 请输入数字序号或 'c' 以选择或自定义 DNS 服务器"
            ;;
        esac
    done

    # 格式化 DNS 服务器配置
    dns_servers_text=$(printf "# NameServer\n%s" "$(echo "$dns_servers" | tr ',' '\n' | sed 's/^/server=/')")

    # 配置 dnsmasq
    {
        # 删除已存在的 NameServer 配置
        sed -i '/^# NameServer/,/^[^#]/d; /^server=/d' "${dns_configure_file}"

        # 在第17行插入新的 NameServer 配置, 并在其后添加一个空行
        sed -i "17r /dev/stdin" "${dns_configure_file}" <<EOF
$dns_servers_text

EOF

        # 其他配置保持不变
        sed -i 's/^#no-resolv$/no-resolv/' "${dns_configure_file}"
        sed -i 's/^#cache-size=150$/cache-size=1000/' "${dns_configure_file}"
        sed -i 's|^#address=/double-click.net/127.0.0.1$|address=/openresty.org/127.0.0.1\naddress=/openresty.com/127.0.0.1\naddress=/openresty.com.cn/127.0.0.1\naddress=/api.openresty.com/127.0.0.1\naddress=/api.openresty.com.cn/127.0.0.1\naddress=/pkg.openresty.com/127.0.0.1\naddress=/pkg.openresty.com.cn/127.0.0.1\naddress=/saas.openresty.com/127.0.0.1\naddress=/saas.openresty.com.cn/127.0.0.1|' "${dns_configure_file}"
        sed -i 's/^#bind-interfaces$/bind-interfaces/' "${dns_configure_file}"
    } || {
        echo -e "\n$ERROR 配置 DNS 文件时发生错误, 但程序将继续执行."
    }

    # 重启 dnsmasq 服务
    systemctl restart dnsmasq

    # 设置本地 DNS 服务器
    rm /etc/resolv.conf
    bash -c 'echo "nameserver 127.0.0.1" > /etc/resolv.conf'

    echo -e "\n$SUCCESS DNS 服务配置完成"
}

## 打印 NTP 服务器列表
function print_ntp_list() {
    local tmp_ntp_name arr_num default_ntp_name_length tmp_ntp_name_length tmp_spaces_nums i j

    echo -e ''

    if [ -x /usr/bin/printf ]; then
        for ((i = 0; i < ${#ntp_list[@]}; i++)); do
            tmp_ntp_name=$(echo "${ntp_list[i]}" | awk -F '@' '{print$1}')
            arr_num=$((i + 1))
            default_ntp_name_length=20  # 默认 NTP 名称打印长度

            # 补齐长度差异 (中文的引号在等宽字体中占 1 格而非 2 格)
            # shellcheck disable=SC1111
            [[ $(echo "${tmp_ntp_name}" | grep -c "“") -gt 0 ]] && ((default_ntp_name_length+=$(echo "${tmp_ntp_name}" | grep -c "“")))
            # shellcheck disable=SC1111
            [[ $(echo "${tmp_ntp_name}" | grep -c "”") -gt 0 ]] && ((default_ntp_name_length+=$(echo "${tmp_ntp_name}" | grep -c "”")))
            [[ $(echo "${tmp_ntp_name}" | grep -c "‘") -gt 0 ]] && ((default_ntp_name_length+=$(echo "${tmp_ntp_name}" | grep -c "‘")))
            [[ $(echo "${tmp_ntp_name}" | grep -c "’") -gt 0 ]] && ((default_ntp_name_length+=$(echo "${tmp_ntp_name}" | grep -c "’")))

            # 非一般字符长度
            # shellcheck disable=SC2001
            tmp_ntp_name_length=$(StringLength "$(echo "${tmp_ntp_name// /}" | sed "s|[0-9a-zA-Z\.\=\:\_\$\'\"-\/\!·]||g;")")

            # 填充空格
            tmp_spaces_nums=$(( (default_ntp_name_length - tmp_ntp_name_length - $(StringLength "${tmp_ntp_name}")) / 2 ))
            for ((j = 1; j <= tmp_spaces_nums; j++)); do
                tmp_ntp_name="${tmp_ntp_name} "
            done

            # 只打印 NTP 名称和序号
            printf " ❖  %-$((default_ntp_name_length + tmp_ntp_name_length))s %2s\n" "${tmp_ntp_name}" "$arr_num)"
        done
    else
        for ((i = 0; i < ${#ntp_list[@]}; i++)); do
            tmp_ntp_name=$(echo "${ntp_list[i]}" | awk -F '@' '{print$1}')
            arr_num=$((i + 1))
            echo -e " ❖  $arr_num. ${tmp_ntp_name}"
        done
    fi
}

## 配置 NTP
function set_up_ntp() {
    local ntp_configure_file="/etc/chrony/chrony.conf"
    local ntp_servers
    local ntp_servers_text

    echo -e "\n$WORKING 正在配置 NTP 服务...\n"

    # 安装 chrony
    apt-get install -y chrony

    # 打印 NTP 服务器列表
    print_ntp_list

    # 用户选择 NTP 服务器
    local CHOICE
    CHOICE=$(echo -e "\n${BOLD}└─ 请选择并输入你想使用的 NTP 服务器 [ 1-${#ntp_list[@]} ] 或输入 'c' 自定义: ${PLAIN}")
    while true; do
        read -rp "${CHOICE}" INPUT
        case "${INPUT}" in
        [1-9] | [1-9][0-9])
            if [[ $INPUT -le ${#ntp_list[@]} ]]; then
                ntp_servers=$(echo "${ntp_list[$((INPUT - 1))]}" | awk -F '@' '{print$2}')
                break
            else
                echo -e "\n$WARN 请输入有效的数字序号"
            fi
            ;;
        [Cc])
            CHOICE=$(echo -e "\n${BOLD}└─ 请输入自定义 NTP 服务器 (多个服务器用逗号分隔): ${PLAIN}")
            read -rp "${CHOICE}" ntp_servers
            break
            ;;
        *)
            echo -e "\n$WARN 请输入数字序号或 'c' 以选择或自定义 NTP 服务器"
            ;;
        esac
    done

    # 格式化 NTP 服务器配置
    ntp_servers_text=$(printf "# NTP Servers\n%s\n" "$(echo "$ntp_servers" | tr ',' '\n' | sed 's/^/server /' | sed 's/$/ iburst/')")

    # 配置 chrony
    {
        # 创建临时文件
        temp_file=$(mktemp)

        # 将新的 NTP 服务器配置写入临时文件, 并在最后添加一个空行
        echo -e "${ntp_servers_text}\n" > "$temp_file"

        # 将原文件内容追加到临时文件中, 同时删除旧的 NTP 配置
        sed '
            /^# NTP Servers$/d;
            /^# Use Debian vendor zone.$/d;
            /^# servers$/d;
            /^server /d;
            /^pool /d;
            /^# Specify time sources/d;
            /^# Use public servers from the pool.ntp.org project/d;
            /^# Please consider joining the pool/d;
            /^# pool /d;
        ' "${ntp_configure_file}" >> "$temp_file"

        # 用临时文件替换原文件
        mv "$temp_file" "${ntp_configure_file}"

    } || {
        echo -e "\n$ERROR 配置 NTP 文件时发生错误, 但程序将继续执行."
        # 如果发生错误, 确保删除临时文件
        [ -f "$temp_file" ] && rm "$temp_file"
    }

    # 重启 chrony 服务
    systemctl restart chrony
    systemctl enable chrony

    # 设置时区
    rm -rf /etc/localtime
    ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime
    timedatectl set-timezone Asia/Shanghai

    echo -e "\n$SUCCESS NTP 服务配置完成"
}

## 卷管理
function manage_disk() {
    # 定义列出未挂载的卷的函数
    function list_unmounted_disks() {
        fdisk -l 2>/dev/null | awk '
        /^Disk \/dev\/[sv]d[b-z]/ {
            disk = $2
            sub(":$", "", disk)
            cmd = "lsblk -no MOUNTPOINT " disk
            cmd | getline mountpoint
            close(cmd)
            if (mountpoint == "") {
                print disk, $3 $4
            }
        }'
    }

    local CHOICE INPUT
    CHOICE=$(echo -e "\n${BOLD}是否需要挂载卷? [Y/n] ${PLAIN}")
    read -rp "${CHOICE}" INPUT
    [[ -z "${INPUT}" ]] && INPUT=Y

    case "${INPUT}" in
    [Yy] | [Yy][Ee][Ss])
        # 1. 列出未挂载的卷
        echo -e "\n$WORKING 正在列出未挂载的卷...\n"
        local unmounted_disks
        unmounted_disks=$(list_unmounted_disks)

        if [[ -z "$unmounted_disks" ]]; then
            echo -e "\n$TIP 没有未挂载的卷"
            return
        else
            echo "$unmounted_disks"
        fi

        # 2. 选择要添加的卷
        CHOICE=$(echo -e "\n${BOLD}请输入要添加的卷 (例如 /dev/vdb): ${PLAIN}")
        read -rp "${CHOICE}" DISK

        if [[ ! -e "$DISK" ]]; then
            echo -e "\n$ERROR 设备 $DISK 不存在"
            return
        fi

        echo -e "\n$WORKING 正在添加卷 $DISK...\n"
        if ! apt-get install -y parted; then
            echo -e "\n$ERROR 安装 parted 失败"
            return
        fi

        if ! partprobe "$DISK"; then
            echo -e "\n$ERROR 添加卷 $DISK 失败"
            return
        fi
        echo -e "\n$SUCCESS 卷 $DISK 添加成功"

        # 3. 询问是否需要格式化
        CHOICE=$(echo -e "\n${BOLD}是否需要格式化卷? [Y/n] ${PLAIN}")
        read -rp "${CHOICE}" FORMAT_CHOICE
        [[ -z "${FORMAT_CHOICE}" ]] && FORMAT_CHOICE=Y

        local FORMAT UUID CURRENT_FS FORCE_CHOICE
        if [[ "${FORMAT_CHOICE}" =~ ^[Yy] ]]; then
            CHOICE=$(echo -e "\n${BOLD}请选择即将格式化的文件系统 [1]ext4 [2]xfs: ${PLAIN}")
            read -rp "${CHOICE}" FORMAT_TYPE
            case "${FORMAT_TYPE}" in
                1) FORMAT="ext4" ;;
                2) FORMAT="xfs" ;;
                *) echo -e "\n$WARN 选择无效将默认使用 xfs"; FORMAT="xfs" ;;
            esac

            echo -e "\n$WORKING 检测现有文件系统类型...\n"
            CURRENT_FS=$(blkid -s TYPE -o value "$DISK" 2>/dev/null || echo "")

            # 安装必要工具
            if [[ "$FORMAT" == "ext4" ]]; then
                apt-get install -y e2fsprogs || { echo -e "\n$ERROR 安装 e2fsprogs 失败"; return; }
            else
                apt-get install -y xfsprogs  || { echo -e "\n$ERROR 安装 xfsprogs 失败"; return; }
            fi

            # 如果已有相同文件系统, 询问是否强制
            if [[ "$CURRENT_FS" == "$FORMAT" ]]; then
                read -rp "检测到 $DISK 已是 $FORMAT, 是否强制格式化? [y/N] " FORCE_CHOICE
                if [[ "$FORCE_CHOICE" =~ ^[Yy] ]]; then
                    if [[ "$FORMAT" == "xfs" ]]; then
                        mkfs.xfs -f "$DISK"
                    else
                        mkfs.ext4 -F "$DISK"
                    fi
                else
                    echo "跳过格式化将保持原有文件系统"
                    UUID=$(blkid -s UUID -o value "$DISK")
                    # 继续挂载流程
                fi
            else
                # 清除旧签名后格式化
                wipefs --all "$DISK" || { echo -e "\n$ERROR wipefs 清除签名失败"; return; }
                if [[ "$FORMAT" == "xfs" ]]; then
                    mkfs.xfs "$DISK"
                else
                    mkfs.ext4 "$DISK"
                fi
            fi

            # 获取 UUID
            UUID=$(blkid -s UUID -o value "$DISK")
            echo -e "\n$SUCCESS 卷 $DISK 格式化为 $FORMAT 并获取 UUID: $UUID"
        else
            UUID=$(blkid -s UUID -o value "$DISK")
        fi

        # 4. 询问是否要挂载卷
        CHOICE=$(echo -e "\n${BOLD}是否要挂载卷? [Y/n] ${PLAIN}")
        read -rp "${CHOICE}" MOUNT_CHOICE
        [[ -z "${MOUNT_CHOICE}" ]] && MOUNT_CHOICE=Y

        if [[ "${MOUNT_CHOICE}" =~ ^[Yy] ]]; then
            CHOICE=$(echo -e "\n${BOLD}请指定挂载路径 (例如 /data): ${PLAIN}")
            read -rp "${CHOICE}" MOUNT_POINT
            if [[ ! -d "$MOUNT_POINT" ]]; then
                mkdir -p "$MOUNT_POINT" || { echo -e "\n$ERROR 创建挂载点 $MOUNT_POINT 失败"; return; }
            fi
            echo -e "\n$WORKING 正在挂载卷...\n"
            echo "UUID=$UUID  $MOUNT_POINT  $FORMAT  defaults  0 0" >> /etc/fstab
            systemctl daemon-reload
            mount -a || { echo -e "\n$ERROR 挂载失败, 请检查 /etc/fstab 文件."; return; }
        fi

        echo -e "\n$SUCCESS 卷挂载完成"
        ;;
    *)
        echo -e "\n$TIP 不进行卷挂载操作"
        ;;
    esac
}

## 修改主机名
function modify_hostname() {
    # shellcheck disable=SC2155
    local current_hostname=$(hostname)

    echo -e "\n$WORKING 当前主机名: $current_hostname"

    local CHOICE
    CHOICE=$(echo -e "\n${BOLD}└─ 是否需要修改主机名? [Y/n] ${PLAIN}")
    read -rp "${CHOICE}" INPUT
    [[ -z "${INPUT}" ]] && INPUT=Y

    case "${INPUT}" in
    [Yy] | [Yy][Ee][Ss])
        local new_hostname
        while true; do
            CHOICE=$(echo -e "\n${BOLD}└─ 请输入新的主机名: ${PLAIN}")
            read -rp "${CHOICE}" new_hostname
            if [[ -z "$new_hostname" ]]; then
                echo -e "\n$WARN 主机名不能为空, 请重新输入."
            elif [[ "$new_hostname" =~ ^[a-zA-Z0-9-]+$ ]]; then
                break
            else
                echo -e "\n$WARN 主机名只能包含字母、数字和连字符, 请重新输入."
            fi
        done

        echo -e "\n$WORKING 正在修改主机名..."

        # 修改 /etc/hostname 文件
        echo "$new_hostname" | tee /etc/hostname > /dev/null

        # 立即修改当前主机名
        hostnamectl set-hostname "$new_hostname"

        echo -e "\n$SUCCESS 主机名已成功修改为: $new_hostname"
        echo -e "$TIP 请注意, 某些更改可能需要重启系统才能完全生效."
        ;;
    [Nn] | [Nn][Oo])
        echo -e "\n$TIP 保持当前主机名不变"
        ;;
    *)
        echo -e "\n$WARN 输入错误, 默认不修改主机名."
        ;;
    esac
}

## 使用 nano 编辑器编辑文件
function edit_file_with_editor() {
    local file_path=$1
    local editor="nano"  # 指定使用 nano 编辑器

    echo -e "\n$WORKING 正在打开文件 $file_path 进行编辑..."

    # 打开文件进行编辑
    $editor "$file_path"

    # 检查编辑器退出状态
    # shellcheck disable=SC2181
    if [ $? -eq 0 ]; then
        echo -e "\n$SUCCESS 文件 $file_path 编辑完成"
    else
        echo -e "\n$FAIL 文件 $file_path 编辑失败"
        return 1
    fi
}

## 修改指定文件
function modify_files() {
    # 修改 /etc/hosts 文件
    edit_file_with_editor "/etc/hosts"
    # shellcheck disable=SC2181
    if [ $? -ne 0 ]; then
        echo -e "\n$ERROR 修改 /etc/hosts 文件时发生错误"
        return 1
    fi

    # 修改 /etc/fstab 文件
    edit_file_with_editor "/etc/fstab"
    # shellcheck disable=SC2181
    if [ $? -ne 0 ]; then
        echo -e "\n$ERROR 修改 /etc/fstab 文件时发生错误"
        return 1
    fi

    # 修改 /etc/cloud/cloud.cfg 文件
    edit_file_with_editor "/etc/cloud/cloud.cfg"
    # shellcheck disable=SC2181
    if [ $? -ne 0 ]; then
        echo -e "\n$ERROR 修改 /etc/cloud/cloud.cfg 文件时发生错误"
        return 1
    fi

    # 修改 ~/.ssh/authorized_keys 文件
    # shellcheck disable=SC2088
    edit_file_with_editor "~/.ssh/authorized_keys"
    # shellcheck disable=SC2181
    if [ $? -ne 0 ]; then
        echo -e "\n$ERROR 修改 ~/.ssh/authorized_keys 文件时发生错误"
        return 1
    fi

    # 修改 /etc/dnsmasq.conf 文件
    edit_file_with_editor "/etc/dnsmasq.conf"
    # shellcheck disable=SC2181
    if [ $? -ne 0 ]; then
        echo -e "\n$ERROR 修改 /etc/dnsmasq.conf 文件时发生错误"
        return 1
    fi

    # 修改 /etc/chrony/chrony.conf 文件
    edit_file_with_editor "/etc/chrony/chrony.conf"
    # shellcheck disable=SC2181
    if [ $? -ne 0 ]; then
        echo -e "\n$ERROR 修改 /etc/chrony/chrony.conf 文件时发生错误"
        return 1
    fi

    # 修改 /etc/ssh/sshd_config 文件
    edit_file_with_editor "/etc/ssh/sshd_config"
    # shellcheck disable=SC2181
    if [ $? -ne 0 ]; then
        echo -e "\n$ERROR 修改 /etc/ssh/sshd_config 文件时发生错误"
        return 1
    fi

    echo -e "\n$SUCCESS 所有文件编辑完成"
}

## 启用 BBR
function enable_bbr() {
    # 检查是否已经启用 BBR
    if lsmod | grep -q bbr; then
        return 0
    fi

    local CHOICE
    CHOICE=$(echo -e "\n${BOLD}└─ 是否需要启用 BBR? [Y/n] ${PLAIN}")
    read -rp "${CHOICE}" INPUT
    [[ -z "${INPUT}" ]] && INPUT=Y

    case "${INPUT}" in
    [Yy] | [Yy][Ee][Ss])
        echo -e "\n$WORKING 正在检查并配置 BBR...\n"

        # 检查内核版本
        if [[ $(uname -r | cut -d. -f1) -lt 4 ]]; then
            echo -e "$ERROR 当前内核版本不支持 BBR, 需要 4.9 及以上版本."
            return 1
        fi

        # 启用 BBR
        echo "net.core.default_qdisc=fq" >> /etc/sysctl.conf
        echo "net.ipv4.tcp_congestion_control=bbr" >> /etc/sysctl.conf
        sysctl -p

        # 验证 BBR 是否成功启用
        if lsmod | grep -q bbr; then
            echo -e "\n$SUCCESS BBR 已成功启用"
            echo -e "$TIP 可能需要重启系统才能完全生效"
        else
            echo -e "\n$ERROR BBR 启用失败, 请检查系统配置."
            return 1
        fi
        ;;
    [Nn] | [Nn][Oo])
        echo -e "\n$TIP 跳过 BBR 启用"
        ;;
    *)
        echo -e "\n$WARN 输入错误, 默认不启用 BBR."
        ;;
    esac
}

## 启用 ZRAM
function enable_zram() {
    # 检查 ZRAM 是否已经启用
    if grep -q zram /proc/swaps; then
        return 0
    fi

    # 获取系统内存大小 (GB, 向上取整)
    local mem_total
    mem_total=$(free -m | awk '/^Mem:/{printf "%.0f\n", $2/1024}')

    # 如果内存大于 8GB, 直接返回
    [ "$mem_total" -gt 8 ] && return 0

    local CHOICE
    CHOICE=$(echo -e "\n${BOLD}└─ 是否需要启用 ZRAM? [Y/n] ${PLAIN}")
    read -rp "${CHOICE}" INPUT
    [[ -z "${INPUT}" ]] && INPUT=Y

    case "${INPUT}" in
    [Yy] | [Yy][Ee][Ss])
        echo -e "\n$WORKING 正在检查并配置 ZRAM..."

        # 根据内存大小设置压缩比例
        local percent
        if [ "$mem_total" -eq 0 ]; then
            # 内存小于 1GB 的情况
            percent=80
        elif [ "$mem_total" -eq 1 ]; then
            percent=70
        elif [ "$mem_total" -eq 2 ]; then
            percent=60
        elif [ "$mem_total" -eq 4 ]; then
            percent=50
        else
            percent=40
        fi

        # 安装 zram-tools
        if ! apt-get install -y zram-tools; then
            echo -e "\n$ERROR 安装 zram-tools 失败"
            return 1
        fi

        # 配置 ZRAM
        local config_file="/etc/default/zramswap"

        # 更新配置
        if [ -f "$config_file" ]; then
            # 更新 ALGO, 保持原有位置
            if grep -q "^#\?ALGO=" "$config_file"; then
                sed -i 's/^#\?ALGO=.*/ALGO=zstd/' "$config_file"
            else
                echo "ALGO=zstd" >> "$config_file"
            fi

            # 更新 PERCENT, 保持原有位置
            if grep -q "^#\?PERCENT=" "$config_file"; then
                sed -i "s/^#\?PERCENT=.*/PERCENT=$percent/" "$config_file"
            else
                echo "PERCENT=$percent" >> "$config_file"
            fi
        else
            # 如果配置文件不存在, 创建新文件
            echo "ALGO=zstd" > "$config_file"
            echo "PERCENT=$percent" >> "$config_file"
        fi

        # 重新加载 ZRAM 服务
        if ! systemctl reload zramswap; then
            echo -e "\n$ERROR ZRAM 服务重载失败"
            return 1
        fi

        # 验证 ZRAM 是否成功启用
        if grep -q zram /proc/swaps; then
            echo -e "\n$SUCCESS ZRAM 已成功启用"
        else
            echo -e "\n$ERROR ZRAM 启用失败, 请检查系统配置."
            return 1
        fi
        ;;
    [Nn] | [Nn][Oo])
        echo -e "\n$TIP 跳过 ZRAM 启用"
        ;;
    *)
        echo -e "\n$WARN 输入错误, 默认不启用 ZRAM."
        ;;
    esac
}

## 安装软件
function install_tools() {
    echo -e "\n$WORKING 正在安装系统工具...\n"

    # 使用数组存储工具列表
    local tools=(net-tools btop nethogs nload pigz dnsutils lshw tcpdump smartmontools trash-cli)

    if ! apt-get install -y "${tools[@]}"; then
        echo -e "\n$ERROR 安装系统工具失败"
        return 1
    fi

    echo -e "\n$SUCCESS 安装系统工具完成"
    return 0
}

## 安装 EdgeZero
function install_edgezero() {
    local CHOICE
    CHOICE=$(echo -e "\n${BOLD}是否安装 EdgeZero? [Y/n] ${PLAIN}")
    read -rp "${CHOICE}" INPUT
    [[ -z "${INPUT}" ]] && INPUT=Y

    case "${INPUT}" in
    [Yy] | [Yy][Ee][Ss])
        echo -e "\n$WORKING 正在安装 EdgeZero...\n"
        if ! curl --proto '=https' --tlsv1.2 -sSf https://mirror.lilh.net/priv/EdgeZero/run.sh | bash; then
            echo -e "\n$ERROR EdgeZero 安装失败"
            return 1
        fi
        echo -e "\n$SUCCESS EdgeZero 安装完成"
        ;;
    *)
        echo -e "\n$TIP 不安装 EdgeZero"
        ;;
    esac
}

function run_end() {
    echo -e "\n---------- 环境配置执行结束 ----------"
    echo -e "\n\033[1;34mPowered by Anbool @2024-09-26\033[0m"
}

## 重启服务器
function ask_reboot() {
    local CHOICE
    CHOICE=$(echo -e "\n${BOLD}是否需要重启服务器? [Y/n] ${PLAIN}")
    read -rp "${CHOICE}" REBOOT_CHOICE
    [[ -z "${REBOOT_CHOICE}" ]] && REBOOT_CHOICE=Y

    case "${REBOOT_CHOICE}" in
    [Yy] | [Yy][Ee][Ss])
        echo -e "\n$WORKING 服务器将立刻重启...\n"
        sleep 1
        reboot
        ;;
    *)
        echo -e "\n$TIP 不重启服务器\n"
        ;;
    esac
}

## 报错退出
function output_error() {
    [ "$1" ] && echo -e "\n$ERROR $1\n"
    exit 1
}

##############################################################################

## 更换基于 Debian 系 Linux 发行版的软件源
function change_mirrors_Debian() {
    function gen_debian_source() {
        echo "deb ${1} ${2} ${3}
# deb-src ${1} ${2} ${3}
deb ${1} ${2}-updates ${3}
# deb-src ${1} ${2}-updates ${3}
deb ${1} ${2}-backports ${3}
# deb-src ${1} ${2}-backports ${3}"
    }
    function gen_debian_security_source() {
        echo "deb ${1} ${2}-security ${3}
# deb-src ${1} ${2}-security ${3}"
    }
    function gen_ubuntu_source() {
        echo "deb ${1} ${2} ${3}
# deb-src ${1} ${2} ${3}
deb ${1} ${2}-updates ${3}
# deb-src ${1} ${2}-updates ${3}
deb ${1} ${2}-backports ${3}
# deb-src ${1} ${2}-backports ${3}
deb ${1} ${2}-security ${3}
# deb-src ${1} ${2}-security ${3}

## 预发布软件源 (不建议启用)
# deb ${1} ${2}-proposed ${3}
# deb-src ${1} ${2}-proposed ${3}"
    }

    local repository_sections # 仓库区域
    local tips="## 默认禁用源码镜像以提高速度, 如需启用请自行取消注释."
    local base_url="https://${SOURCE}/${SOURCE_BRANCH}"
    case "${SYSTEM_JUDGMENT}" in
    "${SYSTEM_DEBIAN}")
        case "${SYSTEM_VERSION_NUMBER}" in
        8 | 9 | 10 | 11)
            repository_sections="main contrib non-free"
            ;;
        *)
            repository_sections="main contrib non-free non-free-firmware"
            ;;
        esac
        if [[ "${SYSTEM_VERSION_CODENAME}" != "sid" ]]; then
            echo "${tips}
$(gen_debian_source "${base_url}" "${SYSTEM_VERSION_CODENAME}" "${repository_sections}")" >>$File_DebianSourceList
            # 处理 debian-security 仓库源
            base_url="https://${SOURCE_SECURITY:-"${SOURCE}"}/${SOURCE_SECURITY_BRANCH:-"${SOURCE_BRANCH}-security"}"
            # shellcheck disable=SC2005
            echo "$(gen_debian_security_source "${base_url}" "${SYSTEM_VERSION_CODENAME}" "${repository_sections}")" >>$File_DebianSourceList
        else
            echo "deb ${base_url} ${SYSTEM_VERSION_CODENAME} ${repository_sections}
# deb-src ${base_url} ${SYSTEM_VERSION_CODENAME} ${repository_sections}" >>$File_DebianSourceList
        fi
        ;;
    "${SYSTEM_UBUNTU}")
        repository_sections="main restricted universe multiverse"
        echo "${tips}
$(gen_ubuntu_source "${base_url}" "${SYSTEM_VERSION_CODENAME}" "${repository_sections}")" >>$File_DebianSourceList
        ;;
    esac
}

##############################################################################
main